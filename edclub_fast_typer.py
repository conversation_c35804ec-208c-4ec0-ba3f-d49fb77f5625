#!/usr/bin/env python3
"""
EdClub 快速自动打字脚本 - 优化版本
专门针对 TypingClub 的字符序列练习优化
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains

class FastEdclubTyper:
    def __init__(self, headless=False):
        """初始化浏览器"""
        print("🚀 初始化快速打字脚本...")
        
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1200,800")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(2)  # 减少等待时间
        
    def open_lesson(self, url):
        """打开课程页面"""
        print(f"🚀 打开课程: {url}")
        self.driver.get(url)
        time.sleep(3)  # 减少等待时间
        
    def find_current_character(self):
        """智能查找当前需要输入的字符"""
        try:
            print("🔍 智能检测当前字符...")

            # 方法1: 使用JavaScript查找高亮元素
            try:
                script = """
                // 查找所有可能的字符元素
                const elements = document.querySelectorAll('div, span, button');
                for (let el of elements) {
                    const style = window.getComputedStyle(el);
                    const text = el.textContent.trim();

                    // 检查是否是单字符且有特殊样式
                    if (text.length === 1 && text.match(/[a-zA-Z0-9;]/)) {
                        const bgColor = style.backgroundColor;
                        const color = style.color;

                        // 检查橙色背景或特殊颜色
                        if (bgColor.includes('255, 165, 0') ||
                            bgColor.includes('orange') ||
                            el.classList.contains('active') ||
                            el.classList.contains('current') ||
                            el.classList.contains('highlight')) {
                            return text;
                        }
                    }
                }
                return null;
                """

                result = self.driver.execute_script(script)
                if result:
                    print(f"🎯 JavaScript找到高亮字符: '{result}'")
                    return result
            except Exception as e:
                print(f"JavaScript方法失败: {e}")

            # 方法2: 查找具有特殊样式的元素
            selectors = [
                "[style*='background-color: rgb(255, 165, 0)']",
                "[style*='background-color: orange']",
                "[style*='background: rgb(255, 165, 0)']",
                "[style*='background: orange']",
                ".active",
                ".current",
                ".highlight",
                ".selected"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            char = element.text.strip()
                            if char and len(char) == 1 and char.isalnum():
                                print(f"🎯 CSS选择器找到字符: '{char}' (选择器: {selector})")
                                return char
                except:
                    continue

            # 方法3: 查找页面顶部区域的字符元素
            try:
                top_elements = self.driver.find_elements(
                    By.XPATH,
                    "//*[position() < 400 and string-length(normalize-space(text()))=1]"
                )

                for element in top_elements:
                    if element.is_displayed():
                        location = element.location
                        if location['y'] < 350:  # 页面顶部
                            char = element.text.strip()
                            if char and len(char) == 1 and char.isalnum():
                                print(f"🎯 顶部区域找到字符: '{char}' (y={location['y']})")
                                return char
            except:
                pass

            print("❌ 未找到当前字符")
            return None

        except Exception as e:
            print(f"❌ 查找字符时出错: {e}")
            return None
    
    def input_character(self, char):
        """快速输入字符"""
        try:
            print(f"⌨️  输入字符: '{char}'")
            
            # 确保页面有焦点
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(0.1)  # 很短的等待
            
            # 直接发送按键
            ActionChains(self.driver).send_keys(char).perform()
            time.sleep(0.2)  # 很短的等待
            
            print(f"✅ 成功输入: '{char}'")
            return True
            
        except Exception as e:
            print(f"❌ 输入失败: {e}")
            return False
    
    def check_continue_button(self):
        """检查并点击Continue按钮"""
        try:
            # 快速查找Continue按钮
            continue_selectors = [
                "//button[contains(text(), 'Continue')]",
                "//button[contains(text(), 'continue')]",
                "//*[contains(text(), 'Continue') and (self::button or self::a or self::div)]"
            ]
            
            for selector in continue_selectors:
                try:
                    button = self.driver.find_element(By.XPATH, selector)
                    if button.is_displayed() and button.is_enabled():
                        print("🎯 点击Continue按钮...")
                        button.click()
                        time.sleep(1)  # 短暂等待页面加载
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"❌ 检查Continue按钮时出错: {e}")
            return False
    
    def is_success_page(self):
        """快速检查是否在成功页面"""
        try:
            page_text = self.driver.page_source.lower()
            return any(indicator in page_text for indicator in [
                "good job", "continue", "next we will practice", "well done"
            ])
        except:
            return False
    
    def run_fast_lesson(self, url, max_attempts=100):
        """运行快速打字课程"""
        try:
            self.open_lesson(url)
            last_char = None
            same_char_count = 0

            for attempt in range(max_attempts):
                print(f"\n🔄 第 {attempt + 1} 次")

                # 检查是否需要点击Continue
                if self.is_success_page():
                    if self.check_continue_button():
                        last_char = None  # 重置字符跟踪
                        same_char_count = 0
                        time.sleep(1)
                        continue

                # 查找当前字符
                char = self.find_current_character()
                if not char:
                    print("⚠️  未找到字符，等待...")
                    time.sleep(0.5)
                    continue

                # 检查是否卡在同一个字符
                if char == last_char:
                    same_char_count += 1
                    if same_char_count > 3:
                        print(f"⚠️  连续{same_char_count}次检测到相同字符'{char}'，可能需要刷新...")
                        # 尝试刷新页面或点击页面其他位置
                        self.driver.refresh()
                        time.sleep(3)
                        same_char_count = 0
                        last_char = None
                        continue
                else:
                    same_char_count = 0
                    last_char = char

                # 输入字符
                if self.input_character(char):
                    # 等待页面响应
                    time.sleep(0.5)

                    # 检查是否出现Continue按钮
                    if self.is_success_page():
                        if self.check_continue_button():
                            last_char = None
                            same_char_count = 0
                            continue
                else:
                    print("⚠️  输入失败，重试...")
                    time.sleep(0.5)

                # 循环间隔
                time.sleep(0.2)

            print(f"📊 完成 {attempt + 1} 次尝试")

        except KeyboardInterrupt:
            print("\n⏹️  用户中断了程序")
        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            print("🔚 浏览器已关闭")

def main():
    """主函数"""
    url = "https://www.edclub.com/sportal/program-3/127.play"
    
    print("🎯 EdClub 快速自动打字脚本")
    print("⚡ 优化版本 - 更快更准确")
    print("⏹️  按 Ctrl+C 可以随时停止程序\n")
    
    typer = FastEdclubTyper(headless=False)
    typer.run_fast_lesson(url)

if __name__ == "__main__":
    main()
