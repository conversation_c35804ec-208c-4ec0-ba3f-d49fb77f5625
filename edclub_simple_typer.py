#!/usr/bin/env python3
"""
EdClub 简单自动打字脚本
专门针对字符序列练习的简化版本
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains

class SimpleEdclubTyper:
    def __init__(self):
        """初始化浏览器"""
        print("🚀 初始化简单打字脚本...")
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1200,800")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(1)
        
    def open_lesson(self, url):
        """打开课程页面"""
        print(f"🚀 打开课程: {url}")
        self.driver.get(url)
        time.sleep(3)
        
    def find_highlighted_character(self):
        """查找高亮字符的简单方法"""
        try:
            # 直接查找所有可能的字符元素
            elements = self.driver.find_elements(By.XPATH, "//*[string-length(text())=1]")
            
            for element in elements:
                if not element.is_displayed():
                    continue
                    
                char = element.text.strip()
                if not char or len(char) != 1:
                    continue
                    
                # 检查元素的位置和样式
                location = element.location
                if location['y'] > 500:  # 跳过键盘区域
                    continue
                    
                # 获取元素的背景色
                try:
                    bg_color = self.driver.execute_script(
                        "return window.getComputedStyle(arguments[0]).backgroundColor;", 
                        element
                    )
                    
                    # 检查是否是橙色背景
                    if "255, 165, 0" in bg_color or "orange" in bg_color.lower():
                        print(f"🎯 找到橙色高亮字符: '{char}'")
                        return char
                        
                except:
                    pass
                    
                # 检查类名
                class_name = element.get_attribute("class") or ""
                if any(cls in class_name.lower() for cls in ["active", "current", "highlight"]):
                    print(f"🎯 找到活跃字符: '{char}' (类名: {class_name})")
                    return char
            
            return None
            
        except Exception as e:
            print(f"❌ 查找字符出错: {e}")
            return None
    
    def type_character(self, char):
        """输入字符"""
        try:
            print(f"⌨️  输入: '{char}'")
            
            # 点击页面确保焦点
            body = self.driver.find_element(By.TAG_NAME, "body")
            body.click()
            time.sleep(0.1)
            
            # 输入字符
            ActionChains(self.driver).send_keys(char).perform()
            time.sleep(0.5)  # 等待页面响应
            
            print(f"✅ 输入完成: '{char}'")
            return True
            
        except Exception as e:
            print(f"❌ 输入失败: {e}")
            return False
    
    def check_and_click_continue(self):
        """检查并点击Continue按钮"""
        try:
            # 查找Continue按钮
            continue_buttons = self.driver.find_elements(
                By.XPATH, 
                "//button[contains(text(), 'Continue')] | //button[contains(text(), 'continue')]"
            )
            
            for button in continue_buttons:
                if button.is_displayed() and button.is_enabled():
                    print("🎯 点击Continue按钮...")
                    button.click()
                    time.sleep(2)
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ 检查Continue按钮出错: {e}")
            return False
    
    def run_typing_practice(self, url, max_rounds=50):
        """运行打字练习"""
        try:
            self.open_lesson(url)
            
            for round_num in range(max_rounds):
                print(f"\n🔄 === 第 {round_num + 1} 轮 ===")
                
                # 检查是否需要点击Continue
                if self.check_and_click_continue():
                    continue
                
                # 查找当前字符
                char = self.find_highlighted_character()
                if not char:
                    print("⚠️  未找到高亮字符，等待...")
                    time.sleep(1)
                    continue
                
                # 输入字符
                if self.type_character(char):
                    # 等待页面更新
                    time.sleep(0.8)
                    
                    # 再次检查Continue按钮
                    self.check_and_click_continue()
                else:
                    print("⚠️  输入失败，重试...")
                    time.sleep(1)
            
            print(f"📊 完成 {max_rounds} 轮练习")
            
        except KeyboardInterrupt:
            print("\n⏹️  用户中断程序")
        except Exception as e:
            print(f"❌ 运行出错: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            self.driver.quit()
            print("🔚 浏览器已关闭")
        except:
            pass

def main():
    """主函数"""
    url = "https://www.edclub.com/sportal/program-3/127.play"
    
    print("🎯 EdClub 简单自动打字脚本")
    print("📝 专门针对字符序列练习优化")
    print("⏹️  按 Ctrl+C 可以随时停止\n")
    
    typer = SimpleEdclubTyper()
    typer.run_typing_practice(url)

if __name__ == "__main__":
    main()
