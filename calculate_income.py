#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANZ银行流水收入计算脚本
计算deposit类型的总收入，排除yan wong kwok的记录
"""

import pandas as pd
import sys
import os

def calculate_income(csv_file_path):
    """
    计算CSV文件中的总收入
    
    Args:
        csv_file_path (str): CSV文件路径
    
    Returns:
        float: 总收入金额
    """
    try:
        # 读取CSV文件
        print(f"📁 正在读取文件: {csv_file_path}")
        
        # 尝试不同的编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(csv_file_path, encoding=encoding)
                print(f"✅ 成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print("❌ 无法读取CSV文件，请检查文件编码")
            return 0
        
        print(f"📊 文件包含 {len(df)} 行数据")
        print(f"📋 列名: {list(df.columns)}")
        
        # 显示前几行数据以便确认格式
        print("\n📝 前5行数据预览:")
        print(df.head())
        
        # 过滤条件
        total_income = 0
        deposit_count = 0
        excluded_count = 0

        # 根据实际CSV格式处理数据
        # 看起来格式是: 日期, 金额, 描述
        columns = list(df.columns)
        print(f"🔍 检测到的列: {columns}")

        # 尝试识别列的含义
        date_col = columns[0] if len(columns) > 0 else None
        amount_col = columns[1] if len(columns) > 1 else None
        desc_col = columns[2] if len(columns) > 2 else None

        print(f"📅 日期列: {date_col}")
        print(f"💰 金额列: {amount_col}")
        print(f"📝 描述列: {desc_col}")

        # 遍历每一行数据
        for index, row in df.iterrows():
            try:
                # 获取描述信息
                description = ""
                if desc_col and not pd.isna(row[desc_col]):
                    description = str(row[desc_col]).lower()

                # 检查是否包含"yan wong kwok"（不区分大小写）
                row_text = str(row).lower()
                if 'yan wong kwok' in row_text or 'yan w kwok' in row_text:
                    excluded_count += 1
                    print(f"⏭️  跳过第{index+1}行: 包含 'yan wong kwok'")
                    continue

                # 获取金额
                amount = 0
                if amount_col and not pd.isna(row[amount_col]):
                    try:
                        amount = float(row[amount_col])
                    except:
                        # 尝试从字符串中提取数字
                        import re
                        amount_str = str(row[amount_col])
                        numbers = re.findall(r'-?[\d,]+\.?\d*', amount_str)
                        if numbers:
                            amount = float(numbers[0].replace(',', ''))

                # 检查是否是收入（正数金额）
                # 在银行流水中，收入通常显示为正数，支出为负数
                if amount > 0:
                    # 进一步检查是否是真正的收入
                    is_income = True

                    # 检查描述中是否包含收入相关关键词
                    income_keywords = ['payment from', 'deposit', 'transfer in', 'salary', 'wage', 'income']
                    expense_keywords = ['atm', 'purchase', 'payment to', 'withdrawal', 'fee']

                    # 如果描述包含支出关键词，可能不是收入
                    for keyword in expense_keywords:
                        if keyword in description:
                            is_income = False
                            break

                    # 如果包含收入关键词，确认是收入
                    for keyword in income_keywords:
                        if keyword in description:
                            is_income = True
                            break

                    if is_income:
                        total_income += amount
                        deposit_count += 1
                        date_str = str(row[date_col]) if date_col else "未知日期"
                        print(f"✅ 第{index+1}行 ({date_str}): +${amount:.2f} - {description[:50]}")
                    else:
                        print(f"⚠️  第{index+1}行: 正数但可能是退款或其他: +${amount:.2f} - {description[:50]}")

            except Exception as e:
                print(f"⚠️  处理第{index+1}行时出错: {e}")
                continue
        
        print(f"\n📈 统计结果:")
        print(f"💰 总收入: ${total_income:.2f}")
        print(f"📊 Deposit记录数: {deposit_count}")
        print(f"⏭️  排除记录数: {excluded_count}")
        
        return total_income
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {csv_file_path}")
        return 0
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")
        return 0

def main():
    """主函数"""
    print("🏦 ANZ银行流水收入计算器")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        # 默认文件路径
        possible_paths = [
            "/Users/<USER>/Documents/ANZ.csv",
            "ANZ.csv",
            "anz.csv",
            "bank_statement.csv"
        ]
        
        csv_file = None
        for path in possible_paths:
            if os.path.exists(path):
                csv_file = path
                break
        
        if not csv_file:
            print("📁 请提供CSV文件路径:")
            csv_file = input("文件路径: ").strip()
    
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        print("\n💡 使用方法:")
        print("python calculate_income.py /path/to/your/ANZ.csv")
        return
    
    # 计算收入
    total_income = calculate_income(csv_file)
    
    print(f"\n🎯 最终结果: ${total_income:.2f}")
    
    # 税务建议
    if total_income > 0:
        print(f"\n📋 税务申报建议:")
        if total_income < 18200:
            print(f"💡 收入 ${total_income:.2f} 低于免税额度 $18,200")
            print("   - 仍需申报但基本不用交税")
            print("   - 记得申报相关费用抵扣")
        else:
            print(f"⚠️  收入 ${total_income:.2f} 超过免税额度")
            print("   - 需要缴纳相应税款")
            print("   - 务必申报所有可抵扣费用")

if __name__ == "__main__":
    main()
